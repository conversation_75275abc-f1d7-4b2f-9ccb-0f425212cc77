import os
import shutil
import re
from pathlib import Path
from datetime import datetime
from nas_config import (
    NAS_DIRECTORIES,
    NETWORK_MAPPING_COMMAND,
    YEAR_RANGE,
    SUPPORTED_EXTENSIONS,
    DATE_PATTERN,
    DELETE_YEAR
)

# 移除网络认证函数，使用手动映射的驱动器

def create_year_month_folders(target_path):
    """创建年月文件夹结构"""
    print(f"在 {target_path} 创建年月文件夹结构...")
    for year in range(YEAR_RANGE["start"], YEAR_RANGE["end"]):
        year_path = os.path.join(target_path, str(year))
        if not os.path.exists(year_path):
            os.makedirs(year_path)
            print(f"创建文件夹: {year}")

        for month in range(1, 13):  # 01-12月
            month_path = os.path.join(year_path, f"{month:02d}")
            if not os.path.exists(month_path):
                os.makedirs(month_path)
                print(f"创建文件夹: {year}/{month:02d}")

def delete_old_files(target_path):
    """删除指定年份的所有文件"""
    print(f"开始删除 {target_path} 中的{DELETE_YEAR}年文件...")
    deleted_count = 0

    try:
        for filename in os.listdir(target_path):
            # 检查文件是否包含指定年份且为支持的图片格式
            if f"_{DELETE_YEAR}" in filename and any(filename.lower().endswith(ext) for ext in SUPPORTED_EXTENSIONS):
                file_path = os.path.join(target_path, filename)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    if deleted_count % 1000 == 0:
                        print(f"已删除 {deleted_count} 个{DELETE_YEAR}年文件...")
                except Exception as e:
                    print(f"删除文件失败: {filename}, 错误: {e}")
    except Exception as e:
        print(f"访问目录失败: {e}")

    print(f"完成！共删除 {deleted_count} 个{DELETE_YEAR}年文件")

def organize_photos_by_date(target_path):
    """按日期整理照片到对应文件夹"""
    print(f"开始整理 {target_path} 中的照片...")
    moved_count = 0
    error_count = 0

    try:
        files = os.listdir(target_path)
        # 统计所有支持的图片文件
        total_files = len([f for f in files if any(f.lower().endswith(ext) for ext in SUPPORTED_EXTENSIONS)])
        print(f"发现 {total_files} 个图片文件需要处理")

        for filename in files:
            # 检查是否为支持的图片格式
            if not any(filename.lower().endswith(ext) for ext in SUPPORTED_EXTENSIONS):
                continue

            # 提取文件名中的日期 (格式: YYYYMMDD)
            # 示例: 9XTFUT218G013776_admin_20220219220351.jpg
            match = re.search(DATE_PATTERN, filename)

            if match:
                date_str = match.group(1)  # 20220219
                year = date_str[:4]  # 2022
                month = date_str[4:6]  # 02

                # 跳过指定删除年份的文件（应该已经删除了）
                if year == DELETE_YEAR:
                    continue

                # 构建目标路径
                target_folder = os.path.join(target_path, year, month)

                # 确保目标文件夹存在
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder)

                # 移动文件
                source_file = os.path.join(target_path, filename)
                target_file = os.path.join(target_folder, filename)

                try:
                    # 检查文件是否是在根目录（避免移动已经整理过的文件）
                    if os.path.dirname(source_file) == target_path:
                        shutil.move(source_file, target_file)
                        moved_count += 1

                        if moved_count % 5000 == 0:
                            print(f"已移动 {moved_count}/{total_files} 个文件...")

                except Exception as e:
                    print(f"移动文件失败: {filename}, 错误: {e}")
                    error_count += 1
            else:
                print(f"无法解析日期的文件: {filename}")
                error_count += 1

    except Exception as e:
        print(f"处理过程中出错: {e}")

    print(f"整理完成！成功移动: {moved_count} 个文件，错误: {error_count} 个")

def add_new_directory():
    """添加新的目录配置"""
    print("\n=== 添加新目录 ===")
    name = input("请输入目录名称: ")
    path = input("请输入目录路径 (例如: Y:\\新目录): ")
    backup_path = input(f"请输入备份路径 (例如: {path}_backup): ") or f"{path}_backup"

    # 获取下一个可用的键
    next_key = str(len(NAS_DIRECTORIES) + 1)

    NAS_DIRECTORIES[next_key] = {
        "name": name,
        "path": path,
        "backup_path": backup_path
    }

    print(f"已添加目录: {name} -> {path}")
    return next_key

def select_directory():
    """选择要操作的目录"""
    print("\n=== 可用目录 ===")
    for key, config in NAS_DIRECTORIES.items():
        description = config.get('description', '')
        if description:
            print(f"{key}. {config['name']} ({config['path']}) - {description}")
        else:
            print(f"{key}. {config['name']} ({config['path']})")

    print(f"{len(NAS_DIRECTORIES) + 1}. 添加新目录")

    choice = input(f"\n请选择目录 (1-{len(NAS_DIRECTORIES) + 1}): ")

    if choice == str(len(NAS_DIRECTORIES) + 1):
        return add_new_directory()
    elif choice in NAS_DIRECTORIES:
        return choice
    else:
        print("无效选项")
        return None

def execute_operations(directory_key, operations):
    """执行指定的操作"""
    config = NAS_DIRECTORIES[directory_key]
    target_path = config["path"]

    print(f"\n开始处理目录: {config['name']} ({target_path})")

    # 检查网络路径是否可访问
    if not os.path.exists(target_path):
        print(f"错误: 无法访问路径 {target_path}，请检查网络连接和权限")
        print(f"请先执行: {NETWORK_MAPPING_COMMAND}")
        return

    if "create_folders" in operations:
        create_year_month_folders(target_path)

    if "delete_2022" in operations:
        confirm = input(f"确认删除所有{DELETE_YEAR}年文件? (输入'YES'确认): ")
        if confirm == "YES":
            delete_old_files(target_path)
        else:
            print(f"删除{DELETE_YEAR}年文件操作已取消")

    if "organize_photos" in operations:
        organize_photos_by_date(target_path)

def main():
    """主函数"""
    print("=== NAS 照片整理工具 (多目录版本) ===")

    while True:
        # 选择目录
        directory_key = select_directory()
        if directory_key is None:
            continue

        config = NAS_DIRECTORIES[directory_key]
        print(f"\n已选择目录: {config['name']} ({config['path']})")

        # 选择操作
        choice = input(f"""
请选择操作:
1. 只创建年月文件夹结构 ({YEAR_RANGE['start']}-{YEAR_RANGE['end']-1})
2. 只删除{DELETE_YEAR}年文件
3. 只整理现有照片到对应文件夹
4. 执行完整流程 (创建文件夹 + 删除{DELETE_YEAR} + 整理照片)
5. 返回目录选择
6. 退出

请输入选项 (1-6): """)

        if choice == "1":
            execute_operations(directory_key, ["create_folders"])
        elif choice == "2":
            execute_operations(directory_key, ["delete_2022"])
        elif choice == "3":
            execute_operations(directory_key, ["organize_photos"])
        elif choice == "4":
            execute_operations(directory_key, ["create_folders", "delete_2022", "organize_photos"])
        elif choice == "5":
            continue
        elif choice == "6":
            print("退出程序")
            break
        else:
            print("无效选项")

        # 询问是否继续
        continue_choice = input("\n是否继续操作其他目录? (y/n): ")
        if continue_choice.lower() != 'y':
            break

if __name__ == "__main__":
    main()