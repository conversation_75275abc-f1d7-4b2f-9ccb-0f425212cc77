import os
import shutil
import re
from pathlib import Path
from datetime import datetime

# NAS路径配置 - 使用Y盘
source_path = r"Y:\主板出货"
backup_path = r"Y:\主板出货_backup"  # 备份路径（可选）

# 移除网络认证函数，使用手动映射的驱动器

def create_year_month_folders():
    """创建年月文件夹结构"""
    print("创建年月文件夹结构...")
    for year in range(2023, 2026):  # 2023-2025
        year_path = os.path.join(source_path, str(year))
        if not os.path.exists(year_path):
            os.makedirs(year_path)
            print(f"创建文件夹: {year}")
        
        for month in range(1, 13):  # 01-12月
            month_path = os.path.join(year_path, f"{month:02d}")
            if not os.path.exists(month_path):
                os.makedirs(month_path)
                print(f"创建文件夹: {year}/{month:02d}")

def delete_2022_files():
    """删除2022年的所有文件"""
    print("开始删除2022年文件...")
    deleted_count = 0
    
    try:
        for filename in os.listdir(source_path):
            if "_2022" in filename and filename.endswith('.jpg'):
                file_path = os.path.join(source_path, filename)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    if deleted_count % 1000 == 0:
                        print(f"已删除 {deleted_count} 个2022年文件...")
                except Exception as e:
                    print(f"删除文件失败: {filename}, 错误: {e}")
    except Exception as e:
        print(f"访问目录失败: {e}")
    
    print(f"完成！共删除 {deleted_count} 个2022年文件")

def organize_photos_by_date():
    """按日期整理照片到对应文件夹"""
    print("开始按日期整理照片...")
    moved_count = 0
    error_count = 0
    
    try:
        files = os.listdir(source_path)
        total_files = len([f for f in files if f.endswith('.jpg')])
        print(f"发现 {total_files} 个jpg文件需要处理")
        
        for filename in files:
            if not filename.endswith('.jpg'):
                continue
                
            # 提取文件名中的日期 (格式: YYYYMMDD)
            # 示例: 9XTFUT218G013776_admin_20220219220351.jpg
            match = re.search(r'_(\d{8})', filename)
            
            if match:
                date_str = match.group(1)  # 20220219
                year = date_str[:4]  # 2022
                month = date_str[4:6]  # 02
                
                # 跳过2022年文件（应该已经删除了）
                if year == "2022":
                    continue
                
                # 构建目标路径
                target_folder = os.path.join(source_path, year, month)
                
                # 确保目标文件夹存在
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder)
                
                # 移动文件
                source_file = os.path.join(source_path, filename)
                target_file = os.path.join(target_folder, filename)
                
                try:
                    # 检查文件是否是在根目录（避免移动已经整理过的文件）
                    if os.path.dirname(source_file) == source_path:
                        shutil.move(source_file, target_file)
                        moved_count += 1
                        
                        if moved_count % 5000 == 0:
                            print(f"已移动 {moved_count}/{total_files} 个文件...")
                            
                except Exception as e:
                    print(f"移动文件失败: {filename}, 错误: {e}")
                    error_count += 1
            else:
                print(f"无法解析日期的文件: {filename}")
                error_count += 1
                
    except Exception as e:
        print(f"处理过程中出错: {e}")
    
    print(f"整理完成！成功移动: {moved_count} 个文件，错误: {error_count} 个")

def main():
    """主函数"""
    print("=== NAS 照片整理工具 ===")
    print(f"源路径: {source_path}")
    
    # 检查网络路径是否可访问
    if not os.path.exists(source_path):
        print("错误: 无法访问NAS路径，请检查网络连接和权限")
        print("请先执行: net use Y: \\\\172.20.0.20\\mes /user:jacky Honor@2021")
        return
    
    choice = input("""
请选择操作:
1. 只创建年月文件夹结构
2. 只删除2022年文件
3. 只整理现有照片到对应文件夹
4. 执行完整流程 (创建文件夹 + 删除2022 + 整理照片)
5. 退出

请输入选项 (1-5): """)
    
    if choice == "1":
        create_year_month_folders()
    elif choice == "2":
        confirm = input("确认删除所有2022年文件? (输入'YES'确认): ")
        if confirm == "YES":
            delete_2022_files()
        else:
            print("操作已取消")
    elif choice == "3":
        organize_photos_by_date()
    elif choice == "4":
        create_year_month_folders()
        confirm = input("确认删除所有2022年文件? (输入'YES'确认): ")
        if confirm == "YES":
            delete_2022_files()
        organize_photos_by_date()
    elif choice == "5":
        print("退出程序")
    else:
        print("无效选项")

if __name__ == "__main__":
    main()