import os
import shutil
import re

NAS_DIRECTORIES = {
    "1": {"name": "主板出货", "path": r"Y:\主板出货"},
    "2": {"name": "客户机", "path": r"Y:\Image\客户机"},
    "3": {"name": "贴背胶", "path": r"Y:\贴背胶"},
    "4": {"name": "后端外观", "path": r"Y:\后端外观"},
    "5": {"name": "整机数据验证", "path": r"Y:\整机数据验证"}
}

def create_year_month_folders(target_path):
    """创建年月文件夹结构 (2023-2025)"""
    print(f"在 {target_path} 创建年月文件夹结构...")
    for year in range(2023, 2026):
        year_path = os.path.join(target_path, str(year))
        if not os.path.exists(year_path):
            os.makedirs(year_path)
            print(f"创建文件夹: {year}")

        for month in range(1, 13):
            month_path = os.path.join(year_path, f"{month:02d}")
            if not os.path.exists(month_path):
                os.makedirs(month_path)
                print(f"创建文件夹: {year}/{month:02d}")

def delete_old_files(target_path, delete_year):
    print(f"现在开始删除 {folder_name}文件夹中的{delete_year}年文件，请勿操作计算机....")
    deleted_count = 0
    folder_name = folder_name
    try:
        for filename in os.listdir(target_path):
            if (f"_{delete_year}" in filename and
                (filename.lower().endswith('.jpg') or filename.lower().endswith('.png') or
                 filename.lower().endswith('.jpeg') or filename.lower().endswith('.bmp'))):
                file_path = os.path.join(target_path, filename)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    if deleted_count % 1000 == 0:
                        print(f"已删除 {deleted_count} 个{folder_name}文件夹中的{delete_year}年文件...")
                except Exception as e:
                    print(f"删除文件失败: {filename}, 错误: {e}")
    except Exception as e:
        print(f"访问目录失败: {e}")

    print(f"恭喜你！共删除 {deleted_count} 个{folder_name}文件夹中的{delete_year}年文件")

def organize_photos_by_date(target_path, skip_year=None):
    print(f"现在开始整理 {target_path.split(":")[1]}文件夹中的的照片...")
    moved_count = 0
    error_count = 0

    try:
        files = os.listdir(target_path)
        # 统计图片文件
        total_files = len([f for f in files if f.lower().endswith(('.jpg', '.png', '.jpeg', '.bmp'))])
        print(f"发现 {total_files} 个图片文件需要处理")

        for filename in files:
            # 检查是否为图片格式
            if not filename.lower().endswith(('.jpg', '.png', '.jpeg', '.bmp')):
                continue

            # 主板出货: 9XTFUT218G013776_admin_20220219220351.jpg
            # 客户机: 签收拍照_ACTHVB2309001339_13327_20241017202525197.png
            date_match = re.search(r'_(\d{8})', filename)  # 8位
            if not date_match:
                date_match = re.search(r'_(\d{14})', filename)  # 14位

            if date_match:
                date_str = date_match.group(1)
                if len(date_str) >= 8:
                    year = date_str[:4]
                    month = date_str[4:6]

                    # 跳过指定年份的文件
                    if skip_year and year == skip_year:
                        continue

                    # 构建目标路径
                    target_folder = os.path.join(target_path, year, month)

                    # 确保目标文件夹存在
                    if not os.path.exists(target_folder):
                        os.makedirs(target_folder)

                    # 移动文件
                    source_file = os.path.join(target_path, filename)
                    target_file = os.path.join(target_folder, filename)

                    try:
                        # 检查文件是否在根目录
                        if os.path.dirname(source_file) == target_path:
                            shutil.move(source_file, target_file)
                            moved_count += 1

                            if moved_count % 1000 == 0:
                                print(f"已移动 {moved_count} 个文件...")

                    except Exception as e:
                        print(f"移动文件失败: {filename}, 错误: {e}")
                        error_count += 1
            else:
                error_count += 1

    except Exception as e:
        print(f"处理过程中出错: {e}")

    print(f"恭喜你！成功移动: {moved_count} 个文件，错误: {error_count} 个")

def select_directory():
    print("\n=== 可用目录 ===")
    for key, config in NAS_DIRECTORIES.items():
        print(f"{key}. {config['name']} ({config['path']})")

    choice = input(f"\n请选择目录 (1-{len(NAS_DIRECTORIES)}): ")

    if choice in NAS_DIRECTORIES:
        return choice
    else:
        print("无效选项")
        return None

def execute_operations(directory_key, operations, delete_year=None):
    """执行指定的操作"""
    config = NAS_DIRECTORIES[directory_key]
    target_path = config["path"]

    print(f"\n开始处理目录: {config['name']}")

    if not os.path.exists(target_path):
        print(f"错误: 无法访问路径 {target_path}，请检查网络连接和权限")
        print("请先登陆NAS，然后再运行本程序")
        return

    if "create_folders" in operations:
        create_year_month_folders(target_path)

    if "delete_old" in operations and delete_year:
        confirm = input(f"确认删除所有{delete_year}年文件吗? (输入'YES'确认): ")
        if confirm == "YES":
            delete_old_files(target_path, delete_year)
        else:
            print(f"删除{delete_year}年文件操作已取消")

    if "organize_photos" in operations:
        organize_photos_by_date(target_path, delete_year)

def main():
    """主函数"""
    print("=== NAS照片整理工具 ===")

    while True:
        directory_key = select_directory()
        if directory_key is None:
            continue

        config = NAS_DIRECTORIES[directory_key]
        print(f"\n已选择目录: {config['name']} 文件夹")

        choice = input("""
请选择操作:
1. 只创建年月文件夹结构
2. 只删除指定年份文件
3. 只整理现有照片到对应文件夹
4. 执行完整流程 (创建文件夹 + 删除指定年份 + 整理照片)
5. 返回目录选择
6. 退出

请输入选项 (1-6): """)

        delete_year = None
        if choice in ["2", "4"]:
            delete_year = input("请输入要删除的年份: ")

        if choice == "1":
            execute_operations(directory_key, ["create_folders"])
        elif choice == "2":
            execute_operations(directory_key, ["delete_old"], delete_year)
        elif choice == "3":
            execute_operations(directory_key, ["organize_photos"])
        elif choice == "4":
            execute_operations(directory_key, ["create_folders", "delete_old", "organize_photos"], delete_year)
        elif choice == "5":
            continue
        elif choice == "6":
            print("退出程序")
            break
        else:
            print("无效选项")

        continue_choice = input("\n是否继续操作其他目录? (y/n): ")
        if continue_choice.lower() != 'y':
            break

if __name__ == "__main__":
    main()