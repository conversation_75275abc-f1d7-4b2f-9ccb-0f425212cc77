#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速添加常见目录的辅助脚本
用于批量添加NAS中常见的照片存储目录
"""

import os
from nas_config import NAS_DIRECTORIES

# 常见的目录配置模板
COMMON_DIRECTORIES = [
    {
        "name": "服务器",
        "path": r"Y:\Image\服务器",
        "description": "服务器相关照片存储目录"
    },
    {
        "name": "网络设备",
        "path": r"Y:\Image\网络设备",
        "description": "网络设备相关照片存储目录"
    },
    {
        "name": "笔记本",
        "path": r"Y:\Image\笔记本",
        "description": "笔记本相关照片存储目录"
    },
    {
        "name": "台式机",
        "path": r"Y:\Image\台式机",
        "description": "台式机相关照片存储目录"
    },
    {
        "name": "显示器",
        "path": r"Y:\Image\显示器",
        "description": "显示器相关照片存储目录"
    },
    {
        "name": "配件",
        "path": r"Y:\Image\配件",
        "description": "配件相关照片存储目录"
    },
    {
        "name": "测试设备",
        "path": r"Y:\Image\测试设备",
        "description": "测试设备相关照片存储目录"
    }
]

def generate_config_code():
    """生成配置代码"""
    print("=== 常见目录配置代码生成器 ===\n")
    
    # 获取当前最大的键值
    max_key = max([int(k) for k in NAS_DIRECTORIES.keys()]) if NAS_DIRECTORIES else 0
    
    print("请选择要添加的目录 (多选请用逗号分隔，如: 1,3,5):")
    for i, dir_config in enumerate(COMMON_DIRECTORIES, 1):
        print(f"{i}. {dir_config['name']} ({dir_config['path']})")
    
    print(f"{len(COMMON_DIRECTORIES) + 1}. 全部添加")
    print("0. 退出")
    
    choice = input("\n请输入选择: ").strip()
    
    if choice == "0":
        print("退出程序")
        return
    
    # 解析选择
    if choice == str(len(COMMON_DIRECTORIES) + 1):
        # 全部添加
        selected_indices = list(range(len(COMMON_DIRECTORIES)))
    else:
        try:
            selected_indices = [int(x.strip()) - 1 for x in choice.split(',')]
            # 验证索引有效性
            selected_indices = [i for i in selected_indices if 0 <= i < len(COMMON_DIRECTORIES)]
        except ValueError:
            print("输入格式错误")
            return
    
    if not selected_indices:
        print("没有选择有效的目录")
        return
    
    # 生成配置代码
    print("\n=== 生成的配置代码 ===")
    print("请将以下代码添加到 nas_config.py 文件的 NAS_DIRECTORIES 字典中:\n")
    
    for i, idx in enumerate(selected_indices):
        dir_config = COMMON_DIRECTORIES[idx]
        key = str(max_key + i + 1)
        
        print(f'    "{key}": {{')
        print(f'        "name": "{dir_config["name"]}",')
        print(f'        "path": r"{dir_config["path"]}",')
        print(f'        "backup_path": r"{dir_config["path"]}_backup",')
        print(f'        "description": "{dir_config["description"]}"')
        print(f'    }},')
    
    print("\n=== 完整的配置文件示例 ===")
    print("nas_config.py 文件应该看起来像这样:\n")
    
    print("NAS_DIRECTORIES = {")
    
    # 显示现有配置
    for key, config in NAS_DIRECTORIES.items():
        print(f'    "{key}": {{')
        print(f'        "name": "{config["name"]}",')
        print(f'        "path": r"{config["path"]}",')
        print(f'        "backup_path": r"{config["backup_path"]}",')
        description = config.get("description", "")
        print(f'        "description": "{description}"')
        print(f'    }},')
    
    # 显示新增配置
    for i, idx in enumerate(selected_indices):
        dir_config = COMMON_DIRECTORIES[idx]
        key = str(max_key + i + 1)
        
        print(f'    "{key}": {{')
        print(f'        "name": "{dir_config["name"]}",')
        print(f'        "path": r"{dir_config["path"]}",')
        print(f'        "backup_path": r"{dir_config["path"]}_backup",')
        print(f'        "description": "{dir_config["description"]}"')
        print(f'    }},')
    
    print("}")
    
    print(f"\n已为您生成 {len(selected_indices)} 个目录的配置代码。")
    print("请手动复制上述代码到 nas_config.py 文件中。")

def auto_add_directories():
    """自动添加目录到配置文件"""
    print("=== 自动添加目录功能 ===")
    print("注意: 此功能会直接修改 nas_config.py 文件")
    
    confirm = input("是否继续? (输入 'YES' 确认): ")
    if confirm != "YES":
        print("操作已取消")
        return
    
    # 这里可以实现自动修改配置文件的逻辑
    # 为了安全起见，暂时只提供代码生成功能
    print("为了安全起见，请使用代码生成功能手动添加配置")
    generate_config_code()

def main():
    """主函数"""
    print("=== NAS目录配置助手 ===")
    
    choice = input("""
请选择功能:
1. 生成配置代码 (推荐)
2. 自动添加目录 (实验性)
3. 退出

请输入选项 (1-3): """)
    
    if choice == "1":
        generate_config_code()
    elif choice == "2":
        auto_add_directories()
    elif choice == "3":
        print("退出程序")
    else:
        print("无效选项")

if __name__ == "__main__":
    main()
