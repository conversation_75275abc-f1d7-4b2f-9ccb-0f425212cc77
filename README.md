# NAS照片整理工具 (多目录版本)

这是一个用于整理存储在网络附加存储(NAS)上的照片文件的Python工具。支持多个目录的批量处理。

## 功能特性

1. **多目录支持**: 支持同时管理多个不同的照片存储目录
2. **创建年月文件夹结构**: 自动创建按年月组织的文件夹结构
3. **删除旧文件**: 批量删除指定年份的旧照片文件
4. **智能照片整理**: 根据文件名中的日期信息，自动将照片移动到对应的年月文件夹
5. **灵活配置**: 通过配置文件轻松添加新的目录和修改设置

## 目录结构

```
NAS/
├── nasFileTools.py      # 主程序文件
├── nas_config.py        # 配置文件
└── README.md           # 说明文档
```

## 配置说明

### 默认支持的目录

1. **主板出货** (`Y:\主板出货`)
   - 主板出货照片存储目录
   
2. **客户机** (`Y:\Image\客户机`)
   - 客户机相关照片存储目录

### 添加新目录

有两种方式添加新目录：

#### 方式1: 通过程序界面添加
1. 运行程序
2. 选择"添加新目录"选项
3. 按提示输入目录信息

#### 方式2: 修改配置文件
编辑 `nas_config.py` 文件，在 `NAS_DIRECTORIES` 字典中添加新条目：

```python
"3": {
    "name": "服务器",
    "path": r"Y:\Image\服务器",
    "backup_path": r"Y:\Image\服务器_backup",
    "description": "服务器相关照片存储目录"
}
```

## 使用方法

### 1. 准备工作

确保已映射网络驱动器：
```cmd
net use Y: \\***********\mes /user:jacky Honor@2021
```

### 2. 运行程序

```bash
python nasFileTools.py
```

### 3. 操作流程

1. **选择目录**: 从可用目录列表中选择要处理的目录
2. **选择操作**: 
   - 选项1: 只创建年月文件夹结构
   - 选项2: 只删除2022年文件
   - 选项3: 只整理现有照片到对应文件夹
   - 选项4: 执行完整流程
   - 选项5: 返回目录选择
   - 选项6: 退出程序

## 配置参数说明

### nas_config.py 配置项

- `NAS_DIRECTORIES`: 目录配置字典
- `NETWORK_MAPPING_COMMAND`: 网络驱动器映射命令
- `YEAR_RANGE`: 年份范围配置 (默认: 2023-2025)
- `SUPPORTED_EXTENSIONS`: 支持的文件扩展名
- `DATE_PATTERN`: 日期正则表达式模式
- `DELETE_YEAR`: 需要删除的年份 (默认: "2022")

## 文件命名规范

程序识别的文件名格式示例：
```
9XTFUT218G013776_admin_20220219220351.jpg
```

其中 `20220219` 部分会被解析为日期信息：
- 年份: 2022
- 月份: 02
- 日期: 19

## 注意事项

1. **备份重要数据**: 在执行删除操作前，请确保已备份重要文件
2. **网络连接**: 确保网络连接稳定，避免文件传输中断
3. **权限检查**: 确保有足够的权限访问和修改目标目录
4. **文件格式**: 默认支持 jpg, jpeg, png, bmp, tiff 格式
5. **批量操作**: 大量文件处理时请耐心等待，程序会显示进度信息

## 故障排除

### 常见问题

1. **无法访问NAS路径**
   - 检查网络连接
   - 确认驱动器映射是否成功
   - 验证用户权限

2. **文件移动失败**
   - 检查目标目录是否存在
   - 确认文件是否被其他程序占用
   - 验证磁盘空间是否充足

3. **日期解析失败**
   - 检查文件名格式是否符合规范
   - 确认日期模式配置是否正确

## 更新日志

### v2.0 (当前版本)
- 新增多目录支持
- 重构代码结构，提高可维护性
- 添加配置文件支持
- 支持更多图片格式
- 改进用户界面和错误处理

### v1.0 (原版本)
- 基础的单目录照片整理功能
- 支持主板出货目录处理
