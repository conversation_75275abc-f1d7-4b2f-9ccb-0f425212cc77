# NAS照片整理工具配置文件
# 可以在这里添加更多需要处理的目录

NAS_DIRECTORIES = {
    "1": {
        "name": "主板出货",
        "path": r"Y:\主板出货",
        "backup_path": r"Y:\主板出货_backup",
        "description": "主板出货照片存储目录"
    },
    "2": {
        "name": "客户机",
        "path": r"Y:\Image\客户机",
        "backup_path": r"Y:\Image\客户机_backup",
        "description": "客户机相关照片存储目录"
    },
    # 可以继续添加更多目录，例如：
    # "3": {
    #     "name": "服务器",
    #     "path": r"Y:\Image\服务器",
    #     "backup_path": r"Y:\Image\服务器_backup",
    #     "description": "服务器相关照片存储目录"
    # },
    # "4": {
    #     "name": "网络设备",
    #     "path": r"Y:\Image\网络设备",
    #     "backup_path": r"Y:\Image\网络设备_backup",
    #     "description": "网络设备相关照片存储目录"
    # }
}

# 网络驱动器映射命令
NETWORK_MAPPING_COMMAND = "net use Y: \\\\***********\\mes /user:jacky Honor@2021"

# 年份范围配置
YEAR_RANGE = {
    "start": 2023,
    "end": 2026  # 不包含此年份，即2023-2025
}

# 文件扩展名配置
SUPPORTED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

# 日期正则表达式模式
DATE_PATTERN = r'_(\d{8})'  # 匹配格式如: _20220219

# 需要删除的年份
DELETE_YEAR = "2022"
